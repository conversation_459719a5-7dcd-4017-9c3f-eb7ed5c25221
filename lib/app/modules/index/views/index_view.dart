import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../colors.dart';
import '../controllers/index_controller.dart';
import '../../home/<USER>/home_view.dart';
import '../../orders/views/orders_view.dart';
import '../../analysis/views/analysis_view.dart';
import '../../categories/views/categories_view.dart';
import '../../settings/views/settings_view.dart';

class IndexView extends GetView<IndexController> {
  const IndexView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => _getCurrentPage()),
      bottomNavigationBar: Obx(() => BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentTab.index,
            onTap: (index) => controller.changeTab(TabIndex.values[index]),
            backgroundColor: ErpColors.bottomNavBackground,
            selectedItemColor: ErpColors.bottomNavActive,
            unselectedItemColor: ErpColors.bottomNavInactive,
            selectedFontSize: 10,
            unselectedFontSize: 10,
            iconSize: 24,
            elevation: 8,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: '主畫面',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list),
                label: '交易紀錄',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.bar_chart),
                label: '報表分析',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.category),
                label: '類別管理',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.settings),
                label: '設定',
              ),
            ],
          )),
    );
  }

  Widget _getCurrentPage() {
    switch (controller.currentTab) {
      case TabIndex.home:
        return const HomeView();
      case TabIndex.orders:
        return const OrdersView();
      case TabIndex.analysis:
        return const AnalysisView();
      case TabIndex.categories:
        return const CategoriesView();
      case TabIndex.settings:
        return const SettingsView();
    }
  }
}
