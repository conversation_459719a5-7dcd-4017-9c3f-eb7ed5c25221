import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../enums.dart';
import 'box_provider.dart';

class PrefProvider {
  final BoxProvider boxProvider;
  final DeviceInfoPlugin deviceInfo;
  final PackageInfo packageInfo;

  PrefProvider({
    required this.boxProvider,
    required this.deviceInfo,
    required this.packageInfo,
  }) {
    _loadThemeMode();
  }

  set themeMode(ThemeMode value) {
    boxProvider.getGsBox(Boxes.settings.name).write('themeMode', value.name);
    // 更新应用主题
    Get.changeThemeMode(value);
  }

  ThemeMode get themeMode {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode == null) return ThemeMode.system;
    return ThemeMode.values.firstWhere(
      (e) => e.name == mode,
      orElse: () => ThemeMode.system,
    );
  }

  /// 加载保存的主题模式
  void _loadThemeMode() {
    final mode = boxProvider.getGsBox(Boxes.settings.name).read('themeMode');
    if (mode != null) {
      final themeMode = ThemeMode.values.firstWhere(
        (e) => e.name == mode,
        orElse: () => ThemeMode.system,
      );
      // 更新应用主题
      Get.changeThemeMode(themeMode);
    }
  }
}
