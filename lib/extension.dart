import 'package:flutter/material.dart';
import 'package:get/get.dart';

extension WidgetX on Widget {
  // 可以把任何 Widget 使用 Dialog 形式呈現
  Future<T?> dialog<T>({
    bool barrierDismissible = true,
    EdgeInsets insetPadding = EdgeInsets.zero,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          // width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 可以把任何 Widget 使用 Sheet 形式呈現
  Future<T?> sheet<T>({
    final bool isDismissible = true,
    final bool enableDrag = false,
    final bool isScrollControlled = true,
    final bool ignoreSafeArea = false,
  }) {
    return Get.bottomSheet<T>(
      SafeArea(child: this),
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      ignoreSafeArea: ignoreSafeArea,
      isDismissible: isDismissible,
      enterBottomSheetDuration: 200.milliseconds,
      exitBottomSheetDuration: 200.milliseconds,
    );
  }
}

extension ThemeModeX on ThemeMode {
  String get display {
    switch (this) {
      case ThemeMode.light:
        return '使用淺色主題';
      case ThemeMode.dark:
        return '使用深色主題';
      case ThemeMode.system:
        return '跟隨系統設定';
      default:
        return '';
    }
  }
}